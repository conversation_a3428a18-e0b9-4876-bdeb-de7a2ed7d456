<?php require_once "config.php";
for($i=1;$i<5;$i++) {
    $cp = "microsoft365$i"."_price";
    $$cp = sprintf("%.2f", $$cp);
}


if ($currency == 'dollar') {
  $currency2 = '$';
}

elseif ($currency == 'pound') {
  $currency2 = '&pound;';
}

elseif ($currency == 'euro') {
  $currency2 = '&euro;';
}
?>
<!DOCTYPE html>
<html>

<head>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-48005285-1"></script>
    <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
        dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'UA-48005285-1');
    </script>

    <title>Email &amp; Office Apps | <?=$company?></title>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
    <?php require_once "includes/admin-head.php"; ?>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js"></script>
</head>

<body>
    <?php require_once "includes/header.php"; ?>
    <div class="container_12">
        <div class="grid_12">
            <div id="tabDetails">
                <?php if ($microsoft365 == 'yes') { ?>
                <h1>Email &amp; Office Apps</h1>
                <p>Microsoft 365 is the productivity cloud that brings together best-in-class Office apps with powerful
                    cloud services, device management, and advanced security.</p>
                <ul class="arrows">
                    <li>Includes Office apps</li>
                    <li>Email &amp; calendaring</li>
                    <li>Chat, calls, &amp; meetings</li>
                    <li>Cloud storage</li>
                </ul>
                <table class="hosting" id="pods">
                    <tr>
                        <th class="empty"></th>
                        <th>
                            <h2><?=$microsoft3651_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($microsoft3651_price, 0, strpos($microsoft3651_price, "."))?><sup><?=strstr($microsoft3651_price, '.')?></sup></span><?=$period?>
                            </div>
                            <a href="https://<?=$hostname?>.partnerconsole.net/execute2/store/domain-search?start=&productCodes=O365-EESEN"
                                class="button">Add to cart</a>
                        </th>
                        <th>
                            <h2><?=$microsoft3652_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($microsoft3652_price, 0, strpos($microsoft3652_price, "."))?><sup><?=strstr($microsoft3652_price, '.')?></sup></span><?=$period?>
                            </div>
                            <a href="https://<?=$hostname?>.partnerconsole.net/execute2/store/domain-search?start=&productCodes=O365-BESSEN"
                                class="button">Add to cart</a>
                        </th>
                        <th>
                            <h2><?=$microsoft3653_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($microsoft3653_price, 0, strpos($microsoft3653_price, "."))?><sup><?=strstr($microsoft3653_price, '.')?></sup></span><?=$period?>
                            </div>
                            <a href="https://<?=$hostname?>.partnerconsole.net/execute2/store/domain-search?start=&productCodes=O365-BPREM"
                                class="button">Add to cart</a>
                        </th>
                        <th>
                            <h2><?=$microsoft3654_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($microsoft3654_price, 0, strpos($microsoft3654_price, "."))?><sup><?=strstr($microsoft3654_price, '.')?></sup></span><?=$period?>
                            </div>
                            <a href="https://<?=$hostname?>.partnerconsole.net/execute2/store/domain-search?start=&productCodes=O365-BUSINESS"
                                class="button">Add to cart</a>
                        </th>
                    </tr>
                    <tr>
                        <td class="item">Web and mobile versions of Office apps</td>
                        <td class="data">-</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                    </tr>
                    <tr>
                        <td class="item">Desktop versions of Office apps</td>
                        <td class="data">-</td>
                        <td class="data">-</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                    </tr>
                    <tr>
                        <td class="item">Mailbox storage</td>
                        <td class="data">50 GB</td>
                        <td class="data">50 GB</td>
                        <td class="data">50 GB</td>
                        <td class="data">-</td>
                    </tr>
                    <tr>
                        <td class="item">Shared online calendar</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">-</td>
                    </tr>
                    <tr>
                        <td class="item">POP &amp; IMAP support for email clients</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">-</td>
                    </tr>
                    <tr>
                        <td class="item">Outlook Web App (OWA)</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">-</td>
                    </tr>
                    <tr>
                        <td class="item">Sync across all devices</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">-</td>
                    </tr>
                    <tr>
                        <td class="item">Microsoft Teams</td>
                        <td class="data">-</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">-</td>
                    </tr>
                    <tr>
                        <td class="item">Microsoft Bookings</td>
                        <td class="data">-</td>
                        <td class="data">-</td>
                        <td class="data">Yes</td>
                        <td class="data">-</td>
                    </tr>
                    <tr>
                        <td class="item">Exhange Online Protection</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">-</td>
                    </tr>
                    <tr>
                        <td class="item">1 TB OneDrive Storage</td>
                        <td class="data">-</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                    </tr>
                    <tr>
                        <td class="item">Microsoft SharePoint</td>
                        <td class="data">-</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">-</td>
                    </tr>
                </table>
                <?php } ?>
            </div>
        </div>
    </div>
    </div>
    <?php require_once "includes/footer.php"; ?>
</body>

</html>