/* Natural Theme */
a, h1, h2 {
    color: #4b4b4b; 
}
.nav {
    background: #444444; /* Old browsers */
    background: -moz-linear-gradient(top, #444444 0%, #333333 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#444444), color-stop(100%,#333333)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #444444 0%,#333333 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #444444 0%,#333333 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #444444 0%,#333333 100%); /* IE10+ */
    background: linear-gradient(to bottom, #444444 0%,#333333 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#444444', endColorstr='#333333',GradientType=0 ); /* IE6-9 */
}
.nav .support {
    color: #F1ECDF;
}
.search a {
    color: #fff;
}
.button {
    color: #fff;
    background: #789e27; /* Old browsers */
    background: -moz-linear-gradient(top, #789e27 0%, #6b8e23 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#789e27), color-stop(100%,#6b8e23)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #789e27 0%,#6b8e23 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #789e27 0%,#6b8e23 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #789e27 0%,#6b8e23 100%); /* IE10+ */
    background: linear-gradient(to bottom, #789e27 0%,#6b8e23 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#789e27', endColorstr='#6b8e23',GradientType=0 ); /* IE6-9 */
}
.button:hover {
    color: #fff;
    background: #729b18; /* Old browsers */
    background: -moz-linear-gradient(top, #729b18 0%, #5b7f26 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#729b18), color-stop(100%,#5b7f26)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #729b18 0%,#5b7f26 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #729b18 0%,#5b7f26 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #729b18 0%,#5b7f26 100%); /* IE10+ */
    background: linear-gradient(to bottom, #729b18 0%,#5b7f26 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#729b18', endColorstr='#5b7f26',GradientType=0 ); /* IE6-9 */
}
.search {
    background: #C7BA99;
}
.aside {
    background: #F1ECDF;
    color: #333;
}
.aside li {
    background: url(/images/list-arrow-b.png) 0 50% no-repeat;
}