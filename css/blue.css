/* Blue Theme */
a, h1, h2 {
    color:#2c639e; 
}
.nav {
    background: #306daa; /* Old browsers */
    background: -moz-linear-gradient(top,  #3d80b9 0%, #306daa 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#3d80b9), color-stop(100%,#306daa)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #3d80b9 0%,#306daa 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #3d80b9 0%,#306daa 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #3d80b9 0%,#306daa 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #3d80b9 0%,#306daa 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3d80b9', endColorstr='#306daa',GradientType=0 ); /* IE6-9 */
}
.nav .support, .search a {
    color: #bbe1f5;
}
.button {
    color: #2c639e;
    background: #a0d5f1; /* Old browsers */
    background: -moz-linear-gradient(top, #a0d5f1 0%, #78c3eb 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#a0d5f1), color-stop(100%,#78c3eb)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #a0d5f1 0%,#78c3eb 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #a0d5f1 0%,#78c3eb 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #a0d5f1 0%,#78c3eb 100%); /* IE10+ */
    background: linear-gradient(to bottom, #a0d5f1 0%,#78c3eb 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a0d5f1', endColorstr='#78c3eb',GradientType=0 ); /* IE6-9 */
}
.button:hover {
    color: #255385;
    background: #92d0f0; /* Old browsers */
    background: -moz-linear-gradient(top,  #92d0f0 0%, #64bbea 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#92d0f0), color-stop(100%,#64bbea)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #92d0f0 0%,#64bbea 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #92d0f0 0%,#64bbea 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #92d0f0 0%,#64bbea 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #92d0f0 0%,#64bbea 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#92d0f0', endColorstr='#64bbea',GradientType=0 ); /* IE6-9 */
}
.search {
    background: #4583b8;
}
.aside {
    background: #62819b;
}