/* Green Theme */
a, h1, h2 {
    color:#336600; 
}
.nav {
    background: #397a00; /* Old browsers */
    background: -moz-linear-gradient(top, #397a00 0%, #3c5900 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#397a00), color-stop(100%,#3c5900)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #397a00 0%,#3c5900 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #397a00 0%,#3c5900 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #397a00 0%,#3c5900 100%); /* IE10+ */
    background: linear-gradient(to bottom, #397a00 0%,#3c5900 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#397a00', endColorstr='#3c5900',GradientType=0 ); /* IE6-9 */
}
.nav .support, .search a {
    color: #ffe400;
}
.button {
    background: #ffe97f; /* Old browsers */
    background: -moz-linear-gradient(top, #ffe97f 0%, #f7e600 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffe97f), color-stop(100%,#f7e600)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #ffe97f 0%,#f7e600 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #ffe97f 0%,#f7e600 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #ffe97f 0%,#f7e600 100%); /* IE10+ */
    background: linear-gradient(to bottom, #ffe97f 0%,#f7e600 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffe97f', endColorstr='#f7e600',GradientType=0 ); /* IE6-9 */
}
.button:hover {
    color: #333;
    background: #ffe46d; /* Old browsers */
    background: -moz-linear-gradient(top, #ffe46d 0%, #eada00 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffe46d), color-stop(100%,#eada00)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #ffe46d 0%,#eada00 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #ffe46d 0%,#eada00 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #ffe46d 0%,#eada00 100%); /* IE10+ */
    background: linear-gradient(to bottom, #ffe46d 0%,#eada00 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffe46d', endColorstr='#eada00',GradientType=0 ); /* IE6-9 */
}
.search {
    background: #669900;
}
.aside {
    background: #9fbc60;
}