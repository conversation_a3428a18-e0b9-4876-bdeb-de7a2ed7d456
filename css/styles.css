/* CSS Reset */
html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}body{line-height:1}ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none}table{border-collapse:collapse;border-spacing:0}

/* 960 Grid */
body{min-width:960px}.container_12{margin-left:auto;margin-right:auto;width:960px}.grid_1,.grid_2,.grid_3,.grid_4,.grid_5,.grid_6,.grid_7,.grid_8,.grid_9,.grid_10,.grid_11,.grid_12{position:relative;display:inline;float:left;margin-left:10px;margin-right:10px}.push_1,.pull_1,.push_2,.pull_2,.push_3,.pull_3,.push_4,.pull_4,.push_5,.pull_5,.push_6,.pull_6,.push_7,.pull_7,.push_8,.pull_8,.push_9,.pull_9,.push_10,.pull_10,.push_11,.pull_11{position:relative}.alpha{margin-left:0}.omega{margin-right:0}.container_12 .grid_1{width:60px}.container_12 .grid_2{width:140px}.container_12 .grid_3{width:220px}.container_12 .grid_4{width:300px}.container_12 .grid_5{width:380px}.container_12 .grid_6{width:460px}.container_12 .grid_7{width:540px}.container_12 .grid_8{width:620px}.container_12 .grid_9{width:700px}.container_12 .grid_10{width:780px}.container_12 .grid_11{width:860px}.container_12 .grid_12{width:940px}.container_12 .prefix_1{padding-left:80px}.container_12 .prefix_2{padding-left:160px}.container_12 .prefix_3{padding-left:240px}.container_12 .prefix_4{padding-left:320px}.container_12 .prefix_5{padding-left:400px}.container_12 .prefix_6{padding-left:480px}.container_12 .prefix_7{padding-left:560px}.container_12 .prefix_8{padding-left:640px}.container_12 .prefix_9{padding-left:720px}.container_12 .prefix_10{padding-left:800px}.container_12 .prefix_11{padding-left:880px}.container_12 .suffix_1{padding-right:80px}.container_12 .suffix_2{padding-right:160px}.container_12 .suffix_3{padding-right:240px}.container_12 .suffix_4{padding-right:320px}.container_12 .suffix_5{padding-right:400px}.container_12 .suffix_6{padding-right:480px}.container_12 .suffix_7{padding-right:560px}.container_12 .suffix_8{padding-right:640px}.container_12 .suffix_9{padding-right:720px}.container_12 .suffix_10{padding-right:800px}.container_12 .suffix_11{padding-right:880px}.container_12 .push_1{left:80px}.container_12 .push_2{left:160px}.container_12 .push_3{left:240px}.container_12 .push_4{left:320px}.container_12 .push_5{left:400px}.container_12 .push_6{left:480px}.container_12 .push_7{left:560px}.container_12 .push_8{left:640px}.container_12 .push_9{left:720px}.container_12 .push_10{left:800px}.container_12 .push_11{left:880px}.container_12 .pull_1{left:-80px}.container_12 .pull_2{left:-160px}.container_12 .pull_3{left:-240px}.container_12 .pull_4{left:-320px}.container_12 .pull_5{left:-400px}.container_12 .pull_6{left:-480px}.container_12 .pull_7{left:-560px}.container_12 .pull_8{left:-640px}.container_12 .pull_9{left:-720px}.container_12 .pull_10{left:-800px}.container_12 .pull_11{left:-880px}.clear{clear:both;display:block;overflow:hidden;visibility:hidden;width:0;height:0}.clearfix:before,.clearfix:after,.container_12:before,.container_12:after{content:'.';display:block;overflow:hidden;visibility:hidden;font-size:0;line-height:0;width:0;height:0}.clearfix:after,.container_12:after{clear:both}.clearfix,.container_12{zoom:1}

/* Whitelabel CSS */
html {
    height:101%;
    margin-bottom:1px;
}
.container_12 {
    padding:0 10px; 
    background:#fff;
    position: relative;
}
body {
    font-family:Helvetica, Arial, sans-serif; 
    font-size:15px; 
    background:#e6e5e5 url(../images/bg-body.png) 0 0 repeat-x;
    font-weight:300; 
    line-height:1.4em;
    color:#333;
}
p {
    margin-bottom:1em
}
a {
    color:#4b4b4b
}
h1,h2, h3, h4, h5, h6 {
    line-height:1.1em
}
h1 {
    color:#4b4b4b; 
    font-size:28px; 
    font-weight:400; 
    margin-bottom:18px
}
h2 {
    color:#4b4b4b; 
    font-size:24px; 
    margin-bottom:18px;
    font-weight: 400;
}
h3 {
    font-size:25px; 
    margin-bottom:8px
}
strong {
    font-weight:400
}
input, select, textarea {
    font-family:Helvetica, Arial, sans-serif; 
    font-weight:300; 
    margin:0; 
    border:0
}
.header img {
    padding: 40px 30px;
}
.nav {
    background: #6a6a6a; /* Old browsers */
    background: -moz-linear-gradient(top,  #6a6a6a 0%, #585858 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#6a6a6a), color-stop(100%,#585858)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #6a6a6a 0%,#585858 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #6a6a6a 0%,#585858 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #6a6a6a 0%,#585858 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #6a6a6a 0%,#585858 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6a6a6a', endColorstr='#585858',GradientType=0 ); /* IE6-9 */
    margin: 0 0 30px -30px;
    width: 1000px; 
    overflow: hidden;
}
.nav ul {
    padding-left: 20px;
}
.nav li {
    float: left; 
}
.nav a {
    display: block;
    padding: 12px 20px; 
    margin-right: 1px;
    font-size: 17px;
    color: #fff; 
    text-decoration: none;
}
.nav a:hover, .nav .login {
    text-decoration: none;
    background:rgba(255,255,255,.1)
}
.nav .login:hover {
    text-decoration: none;
    background:rgba(255,255,255,.2)
}
.nav .support {
    color: #ffdd80;
}
.cnr-l, .cnr-r {
    width: 10px; 
    height: 10px; 
    position: absolute; 
    bottom: 20px
}
.cnr-l {
    background: url(../images/nav-corner-l.png) 0 0 no-repeat;
    left: -30px; 
}
.cnr-r {
    background: url(../images/nav-corner-r.png) 0 0 no-repeat;
    right: -30px;
}
.section {
    padding: 20px 20px 20px 30px;
}
.section li {
    list-style: disc;
    margin: 0 0 1em 20px;
}
.section h3 {
    font-size:20px; 
    font-weight:400;
}
.footer {
    background: #4c4c4c;
    color: #fff;
    font-size: 13px;
    padding: 20px 10px;
}
.footer li, .footer p, .footer a {
    color: #eee;
}
.footer li {
    float: left;
    padding-right: 10px;
    margin-right: 10px;
    border-right: 1px solid #aaa;
}
.footer a {
    text-decoration: none;
    line-height: 1em;
}
.footer a:hover {
    text-decoration: underline;
}
.footer li:last-child {
    margin: 0;
    padding: 0;
    border: 0;
}
.footer ul {
    float: right;
}
.button {
    font-size: 16px;
    padding: 10px 15px;
    color: #4b4b4b;
    text-decoration: none;
    font-weight: 600;
    float: right;
    background: #ffcf4d; /* Old browsers */
    background: -moz-linear-gradient(top, #ffcf4d 0%, #ffba01 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffcf4d), color-stop(100%,#ffba01)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #ffcf4d 0%,#ffba01 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #ffcf4d 0%,#ffba01 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #ffcf4d 0%,#ffba01 100%); /* IE10+ */
    background: linear-gradient(to bottom, #ffcf4d 0%,#ffba01 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffcf4d', endColorstr='#ffba01',GradientType=0 ); /* IE6-9 */
    -moz-box-shadow: 4px 4px 0 rgba(0, 0, 0, .1);
    -webkit-box-shadow: 4px 4px 0 rgba(0, 0, 0, .1);
    box-shadow: 4px 4px 0 rgba(0, 0, 0, .1);
    cursor: pointer;
    text-transform: uppercase;
}
}
.button:hover, {
    color: #191919;
    background: #ffca4d; /* Old browsers */
    background: -moz-linear-gradient(top,  #ffca4d 0%, #ffb300 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffca4d), color-stop(100%,#ffb300)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #ffca4d 0%,#ffb300 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #ffca4d 0%,#ffb300 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #ffca4d 0%,#ffb300 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #ffca4d 0%,#ffb300 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffca4d', endColorstr='#ffb300',GradientType=0 ); /* IE6-9 */
}
.arrow {
    background: url(../images/link-arrow.png) 0 0 no-repeat;
    text-indent:-99999px;
    float:right;
    line-height: 0;
    height: 33px;
    width: 50px;
}
.arrows li {
    background:url(../images/list-arrow-b.png) 0 50% no-repeat;
    padding-left:16px;
    margin-bottom: 6px;
}
.search {
    background: #6a6a6a;
    padding: 30px;
    margin-bottom: 20px;
    color: #fff;
    overflow: hidden;
}
.search h2 {
    font-size: 18px;
    margin-bottom: 12px;
    color: #fff;
}
.search a {
    color: #ffdd80;
    float: right;
    font-size: 13px;
}
.search span {
    color: #000;
    font-size: 18px;
}
.search .inner, .contact-form input[type=text], .contact-form textarea {
    background: #fff;
    padding: 10px 20px;
    -moz-box-shadow: inset 1px 2px 6px #DDD;
    -webkit-box-shadow: inset 1px 2px 6px #DDD;
    box-shadow: inset 1px 2px 6px #DDD;
}
.search .inner {
    float: left;
}
.search #domain {
    font-size: 18px;
    width: 527px;
    border-right: 1px solid #C4C4C4;
    padding: 6px 0;
    margin-left: -3px;
}
.search select {
    -webkit-appearance: none;
    font-size: 18px;
    padding: 0 18px 0 10px;
    background: #fff url(../images/select-arrow.png) 100% 50% no-repeat;
}
.search .button {
    font-size: 18px;
    padding: 16px 36px;
}
.aside {
    padding: 30px;
    background: #7c7c7c;
    color: #fff;
    overflow: hidden;
    margin-bottom: 20px;
}
.aside ul {
    margin-bottom:20px;
}
.aside li {
    background:url(../images/list-arrow-w.png) 0 50% no-repeat;
    padding-left:16px;
    margin-bottom: 6px;
}
.aside h3 {
    text-transform: uppercase;
}
.aside p {
    line-height: 1.2em;
}
.privacy {
    margin-bottom: 20px;
    background:url(../images/domain-privacy.png) 0 0 no-repeat !important;
    height:206px;
    color:#fff !important;
}
.privacy h3, .privacy p {
    width: 115px;
}
.link-box {
    padding: 30px 30px 15px;
    margin-bottom: 1px;
    overflow: hidden;
    background: #f5f5f5; /* Old browsers */
    background: -moz-linear-gradient(top,  #f5f5f5 50%, #e7e7e7 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(50%,#f5f5f5), color-stop(100%,#e7e7e7)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #f5f5f5 50%,#e7e7e7 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #f5f5f5 50%,#e7e7e7 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #f5f5f5 50%,#e7e7e7 100%); /* IE10+ */
    background: linear-gradient(to bottom,  #f5f5f5 50%,#e7e7e7 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f5f5f5', endColorstr='#e7e7e7',GradientType=0 ); /* IE6-9 */
}
.link-box:last-of-type {
    margin-bottom:20px
}
.link-box h3 {
    font-size: 18px;
    font-weight: 400;
    text-transform: uppercase;
}
.link-box p {
    margin: 0;
    line-height: 1.2em;
}
.link-box a{
    text-decoration:none;
}
.price-grid {
    background: #f5f5f5;
    padding: 30px;
    overflow:hidden;
}
.price-grid div {
    padding: 30px 20px 20px;
    background: #fff;
    border: 1px solid #e3e3e3;
    font-size: 16px;
    text-align: center;
    width: 145px;
    float:left;
    display:inline;
    margin: 0 -1px -1px 0;
    font-weight: 400;
}
.price-grid a {
    float:right;
    font-size:13px;
}
.price-grid span {
    font-size: 52px;
    font-weight: 600;
    font-family: Tahoma, Verdana, Geneva, sans-serif;
}
.price-grid sup {
    font-size: .5em;
    vertical-align: super;
}
.price-grid + .section h3 {
    margin:20px 0 16px;
}
.conditions {
    font-size: 12px;
    color: #aaa;
    clear: both;
}
.all-prices div {
    border-bottom: 1px solid #ccc;
    padding: 6px 0;
    width: 71px;
    float: left;
    display: inline;
    width: 88px;
}
.all-prices div:nth-of-type(even) {
    margin-right: 20px;
    text-align:right;
}
.all-prices div:nth-of-type(6n) {
    margin-right: 0;
}
.all-prices .conditions {
    padding-top: 20px;
}
#tabContainer li {
    float: left;
    padding: 15px 30px;
    font-size:18px;
    text-transform: uppercase;
}
#tabContainer {
    overflow: hidden;
}
#tabContainer .active {
    background: #f5f5f5;
}
#tabContainer .active a {
    color: #737373;
    text-decoration: none;
}
#tabDetails {
    background: #f5f5f5;
    padding: 30px;
    margin-bottom:20px;
    overflow: hidden;
}
#tabDetails ul {
    float:left;
    display:inline;
    width:539px;
    margin-left:20px;
    padding-left:20px;
    border-left:1px solid #e3e3e3
}
#tabDetails p {
    float:left;
    display:inline;
    width:300px;
}
#tabDetails table {
    clear: both;
    margin-top:40px;
    float: left;
    width: 100%;
}
#tabDetails th {
    background: #fff;
    padding: 20px 0 30px;
    border: 1px solid #e3e3e3;
}
#tabDetails th h2 {
    font-size: 18px;
    text-transform: uppercase;
}
#tabDetails td {
    background: #fff;
    padding: 10px 0;
    text-align: center;
    border: 1px solid #e3e3e3;
}
#tabDetails .item {
    background: 0;
    border-left: 0;
    width: 190px;
    text-align: left;
}
#tabDetails tr:last-child .item {
    border-bottom: 0;
}
#tabDetails .empty {
    background: 0;
    border:0;
}
#tabDetails .button {
    float: none;
}
#tabDetails .price {
    padding-bottom: 20px;
    font-size: 16px;
    text-align: center;
}
#tabDetails .price span {
    font-size: 52px;
    font-weight: 600;
    font-family: Tahoma, Verdana, Geneva, sans-serif;
}
#tabDetails .price sup {
    font-size: .5em;
    vertical-align: super;
}
.contact {
    background: #f5f5f5;
    padding: 30px 30px 15px;
    overflow: hidden;
    margin: -20px 0 20px;
}
.contact span {
    float: left;
    width: 130px;
}
.contact p {
    margin-left: 130px;
    font-weight: 400;
}
.contact div {
    position:relative;
    display:inline;
    float:left;
}
.contact .phonemail {
    padding-right:40px
}
.contact .address span {
    float: none;
}
.contact .address p {
    margin-top: 6px;
    margin-left: 0;
}
.contact-form label {
    display: block;
}
.contact-form input[type=text], .contact-form textarea {
    padding: 8px 12px;
    font-size: 15px;
    width: 216px;
    margin: 4px 0 10px;
}
.contact-form span.error {
    float: right;
    font-size: 13px;
    background: #c00;
    padding: 2px 6px;
    margin-bottom: -4px;
}
.contact-form input.error, .contact-form textarea.error {
    border: 2px solid #c00;
    width: 212px;
}
.faq {
    background: #f5f5f5;
    padding: 30px;
    margin-bottom:10px;
}
.faq:last-child {
    margin-bottom:20px;
}
.faq-list li {
    margin-bottom:1em;
}
.faq-list > li {
    margin-bottom:20px;
    padding-bottom:20px;
    border-bottom:1px solid #ccc;
    margin-left:0;
    list-style:none;
    overflow:hidden;
}
.faq-list > li:last-child {
    border:0;
    margin:0;
}
.faq-list ul > li {
    margin-left:20px;
    list-style:disc
}
.faq-list ol > li {
    margin-left:20px;
    list-style:decimal
}
.faq-list .none > li {
    list-style:none;
    margin-left:0;
}
.faq-list .tight ol, .faq-list .tight ul {
    margin-top: 0;
}
.faq-list .tight li {
    margin-left: 40px;
    margin-bottom: 0;
}
.faq-list li ol, .faq-list li ul, .faq-list li p {
    margin-top:1em;
}
.faq-list > li > strong {
    font-size:20px;
}
.faq-list span {
    text-decoration:underline
}
.faq-list em {
    font-style: italic;
    font-weight: 600;
}
.faq-list .top {
    float: right;
}
.back {
    background:url(../images/list-arrow-b-l.png) 0 50% no-repeat;
    float: right;
    padding-left:20px;
}
#captcha {
  width: 100%;
  margin-bottom: 10px;
  display: block;
}
#different {
  float: right;
  color: #fff;
  font-size: 13px;
}
@-moz-document url-prefix() {
    .search select {background:none; padding-right:0}
    .search .button {height:51px}
}

/**/