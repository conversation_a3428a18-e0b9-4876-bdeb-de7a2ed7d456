<?php require_once "config.php";
for($i=1;$i<5;$i++) {
    $cp = "cpanel$i"."_price";
    $ch = "cpanel_legacy$i"."_price";
    $$cp = sprintf("%.2f", $$cp);
    $$ch = sprintf("%.2f", $$ch);
}

if ($currency == 'dollar') {
  $currency2 = '$';
}

elseif ($currency == 'pound') {
  $currency2 = '&pound;';
}

elseif ($currency == 'euro') {
  $currency2 = '&euro;';
}
?>
<!DOCTYPE html>
<html>

<head>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-48005285-1"></script>
    <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
        dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'UA-48005285-1');
    </script>

    <title>cPanel Web Hosting | <?=$company?></title>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
    <?php require_once "includes/admin-head.php"; ?>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js"></script>
</head>

<body>
    <?php require_once "includes/header.php"; ?>
    <div class="container_12">
        <div class="grid_12">
            <div class="section">
                <h1>Web Hosting</h1>
                <p>Whether you need a full-featured top-of-the-line hosting product, a simple hosting platform for your
                    new website, or just a professional email address, we have the product for you. Check out our full
                    range of hosting products below. All plans are hosted on world-class technology, with the security
                    and reliability of Australia's largest hosting infrastructure.</p>
            </div>
        </div>
    </div>
    <div class="container_12">
        <div class="grid_12">
            <div id="tabDetails">
                <?php if (($cpanel_legacy_hosting == 'yes') || ($cpanel_hosting == 'yes')) { ?>
                <h2>cPanel Web Hosting</h2>
                <p>cPanel is an industry standard, off-the-shelf product used all over the web. The easy-to-use online
                    interface is accessible from wherever you are, allowing you to easily install applications, manage
                    domains and check website statistics.</p>
                <ul class="arrows">
                    <li>Linux operating system with Apache webserver</li>
                    <li>Install apps easily - WordPress, Magento, Joomla &amp; Drupal</li>
                    <li>Many popular scripting languages - PHP5, Perl/CGI &amp; Python</li>
                    <li>Email features - Webmail, Anti-spam, Anti-virus &amp; Catchall Email Address</li>
                    <li>Easy management - The Console, Manage DNS/Zones, Web Stats &amp; SSL</li>
                </ul>
                <?php if ($cpanel_hosting == 'yes') { ?>
                <table class="hosting" id="pods">
                    <tr>
                        <th class="empty" th class="empty" width="10%"></th>
                        <th>
                            <h2><?=$cpanel1_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($cpanel1_price, 0, strpos($cpanel1_price, "."))?><sup><?=strstr($cpanel1_price, '.')?></sup></span><?=$period?>
                            </div>
                            <a href="https://<?=$hostname?>.partnerconsole.net/execute2/store/domain-search?start=&productCodes=CPANEL-ECON-HOSTING"
                                class="button">Add to cart</a>
                        </th>
                        <th width="22.5%">
                            <h2><?=$cpanel2_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($cpanel2_price, 0, strpos($cpanel2_price, "."))?><sup><?=strstr($cpanel2_price, '.')?></sup></span><?=$period?>
                            </div>
                            <a href="https://<?=$hostname?>.partnerconsole.net/execute2/store/domain-search?start=&productCodes=CPANEL-PREM-HOSTING"
                                class="button">Add to cart</a>
                        </th>
                        <th width="22.5%">
                            <h2><?=$cpanel3_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($cpanel3_price, 0, strpos($cpanel3_price, "."))?><sup><?=strstr($cpanel3_price, '.')?></sup></span><?=$period?>
                            </div>
                            <a href="https://<?=$hostname?>.partnerconsole.net/execute2/store/domain-search?start=&productCodes=CPANEL-PERF-HOSTING"
                                class="button">Add to cart</a>
                        </th>
                        <th width="22.5%">
                            <h2><?=$cpanel4_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($cpanel4_price, 0, strpos($cpanel4_price, "."))?><sup><?=strstr($cpanel4_price, '.')?></sup></span><?=$period?>
                            </div>
                            <a href="https://<?=$hostname?>.partnerconsole.net/execute2/store/domain-search?start=&productCodes=CPANEL-DLX-HOSTING"
                                class="button">Add to cart</a>
                        </th>
                    </tr>
                    <tr>
                        <td class="item">Performance</td>
                        <td class="data">-</td>
                        <td class="data">-</td>
                        <td class="data">2x</td>
                        <td class="data">4x</td>
                    </tr>
                    <tr>
                        <td class="item">Disk space</td>
                        <td class="data">15 GB SSD</td>
                        <td class="data">75 GB SSD</td>
                        <td class="data">125 GB SSD</td>
                        <td class="data">200 GB SSD</td>
                    </tr>
                    <tr>
                        <td class="item">Monthly data limit</td>
                        <td class="data">25 GB</td>
                        <td class="data">200 GB</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="item">Email accounts</td>
                        <td class="data">50</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="item">Mailbox size</td>
                        <td class="data">Included in 15 GB</td>
                        <td class="data">Included in 75 GB</td>
                        <td class="data">Included in 125 GB</td>
                        <td class="data">Included in 200 GB</td>
                    </tr>
                    <tr>
                        <td class="item">Number of databases</td>
                        <td class="data">15</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="item">Database storage</td>
                        <td class="data">Included in 15 GB</td>
                        <td class="data">Included in 75 GB</td>
                        <td class="data">Included in 125 GB</td>
                        <td class="data">Included in 200 GB</td>
                    </tr>
                    <tr>
                        <td class="item">Subdomains</td>
                        <td class="data">10</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="item">Add-on domains</td>
                        <td class="data">2</td>
                        <td class="data">10</td>
                        <td class="data">20</td>
                        <td class="data">20</td>
                    </tr>
                    <tr>
                        <td class="item">Alias domains</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="item">FTP Accounts</td>
                        <td class="data">15</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="item">LiteSpeed</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                    </tr>
                    <tr>
                        <td class="item">JetBackup</td>
                        <td class="data">No</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                        <td class="data">Yes</td>
                    </tr>
                </table>
                <?php } if ($cpanel_legacy_hosting == 'yes') { ?>
                <h2 style="clear: both; display: table; padding-top:60px;">Legacy Plans</h2>
                <table class="hosting" id="pods">
                    <tr>
                        <th class="empty"></th>
                        <th>
                            <h2><?=$cpanel_legacy1_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($cpanel_legacy1_price, 0, strpos($cpanel_legacy1_price, "."))?><sup><?=strstr($cpanel_legacy1_price, '.')?></sup></span><?=$period?>
                            </div>
                        </th>
                        <th>
                            <h2><?=$cpanel_legacy2_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($cpanel_legacy2_price, 0, strpos($cpanel_legacy2_price, "."))?><sup><?=strstr($cpanel_legacy2_price, '.')?></sup></span><?=$period?>
                            </div>
                        </th>
                        <th>
                            <h2><?=$cpanel_legacy3_name?></h2>
                            <div class="price">
                                <span><sup><?=$currency2?></sup><?=substr($cpanel_legacy3_price, 0, strpos($cpanel_legacy3_price, "."))?><sup><?=strstr($cpanel_legacy3_price, '.')?></sup></span><?=$period?>
                            </div>
                        </th>
                    </tr>
                    <tr>
                        <td class="item">Disk space</td>
                        <td class="data">10 GB</td>
                        <td class="data">50 GB</td>
                        <td class="data">100 GB</td>
                    </tr>
                    <tr>
                        <td class="item">Monthly data limit</td>
                        <td class="data">20 GB</td>
                        <td class="data">100 GB</td>
                        <td class="data">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="item">Email accounts</td>
                        <td class="data">20</td>
                        <td class="data">50</td>
                        <td class="data">500</td>
                    </tr>
                    <tr>
                        <td class="item">Mailbox size</td>
                        <td class="data">Included in 10 GB</td>
                        <td class="data">Included in 50 GB</td>
                        <td class="data">Included in 100 GB</td>
                    </tr>
                    <tr>
                        <td class="item">Number of databases</td>
                        <td class="data">2</td>
                        <td class="data">10</td>
                        <td class="data">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="item">Database storage</td>
                        <td class="data">Included in 10 GB</td>
                        <td class="data">Included in 50 GB</td>
                        <td class="data">Included in 100 GB</td>
                    </tr>
                    <tr>
                        <td class="item">Subdomains</td>
                        <td class="data">5</td>
                        <td class="data">10</td>
                        <td class="data">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="item">Add-on domains</td>
                        <td class="data">2</td>
                        <td class="data">10</td>
                        <td class="data">20</td>
                    </tr>
                    <tr>
                        <td class="item">Alias domains</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                        <td class="data">Unlimited</td>
                    </tr>
                </table>
                <?php }} ?>
            </div>
        </div>
    </div>
    </div>
    <?php require_once "includes/footer.php"; ?>
</body>

</html>