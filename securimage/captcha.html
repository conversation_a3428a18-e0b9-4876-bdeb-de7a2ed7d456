<!DOCTYPE html>
<html>
<!--
    The following is a complete HTML snippet that can be used on your form for
    displaying the captcha image.

    This HTML snippet generates a CAPTCHA image, HTML5 audio controls, a button
    to refresh the image and audio, as well as an input field for accepting the
    captcha code input.

    Modify to fit your needs and the website appearance.  The audio section can
    also be removed if desired.

    The same code is given twice, first as a whole and then repeated with
    inline comments describing the individual elements.

    Most of this code can be generated automatically with many customization
    options by using the function Securimage::getCaptchaHtml() instead.
-->

<head>
  <meta charset="utf-8">
  <title>Sample CAPTCHA HTML</title>
  <link rel="stylesheet" href="securimage.css" media="screen">
</head>
<body>

<h4>Note: Running this on a PHP enabled server will likely work, but you should use example_form.php for testing instead.</h4>

<div>
  <img style="float: left; padding-right: 5px" id="captcha_image" src="securimage_show.php?<?php echo md5(uniqid(time)) ?>" alt="CAPTCHA Image">
  <div id="captcha_image_audio_div">
    <audio id="captcha_image_audio" preload="none" style="display: none">
      <!-- <source id="captcha_image_source_mp3" src="securimage_play.php?id=1234&amp;format=mp3" type="audio/mpeg"> -->
      <source id="captcha_image_source_wav" src="securimage_play.php?id=1234" type="audio/wav">
      <object type="application/x-shockwave-flash" data="securimage_play.swf?bgcol=%23ffffff&amp;icon_file=images%2Faudio_icon.png&amp;audio_file=securimage_play.php" height="32" width="32">
        <param name="movie" value="securimage_play.swf?bgcol=%23ffffff&amp;icon_file=images%2Faudio_icon.png&amp;audio_file=securimage_play.php" />
      </object>
    </audio>
  </div>
  <div id="captcha_image_audio_controls">
    <a tabindex="-1" class="captcha_play_button" href="securimage_play.php?id=1234 ?>" onclick="return false">
      <img class="captcha_play_image" height="32" width="32" src="images/audio_icon.png" alt="Play CAPTCHA Audio" style="border: 0px">
      <img class="captcha_loading_image rotating" height="32" width="32" src="images/loading.png" alt="Loading audio" style="display: none">
    </a>
    <noscript>Enable Javascript for audio controls</noscript>
  </div>

  <a tabindex="-1" style="border: 0" href="#" title="Refresh Image" onclick="document.getElementById('captcha_image').src = 'securimage_show.php?' + Math.random(); captcha_image_audioObj.refresh(); this.blur(); return false">
    <img height="32" width="32" src="images/refresh.png" alt="Refresh Image" onclick="this.blur()" style="border: 0px; vertical-align: bottom" />
  </a>
  <br>

  <script type="text/javascript" src="securimage.js"></script>
  <script type="text/javascript">
    captcha_image_audioObj = new SecurimageAudio({ audioElement: 'captcha_image_audio', controlsElement: 'captcha_image_audio_controls' });
  </script>
  <div style="clear: both"></div>

  <label for="captcha_code">Type the text:</label>
  <input type="text" name="captcha_code" id="captcha_code">
</div>


<!-- and once again with comments for clarity -->
<!-- all IDs have been changed so this *should* work on a PHP enabled server -->
<!-- example_form.php should really be used for testing though -->
<br><br>


<div>
  <!-- captcha image element; the <?php echo md5(...) ?> code throughout is to prevent caching issues -->
  <img style="float: left; padding-right: 5px" id="captcha_image2" src="securimage_show.php?<?php echo md5(uniqid(time)) ?>" alt="CAPTCHA Image">
  
  <!-- invisible div containing captcha audio tag, and optional flash fallback code -->
  <div id="captcha_image_audio_div2">
    <!-- the audio tag -->
    <audio id="captcha_image2_audio" preload="none" style="display: none">
      <!-- mp3 source tag - uncomment if you have LAME installed -->
      <!-- <source id="captcha_image2_source_mp3" src="securimage_play.php?id=<?php echo uniqid() ?>&amp;format=mp3" type="audio/mpeg"> -->

      <!-- wav source tag -->
      <source id="captcha_image2_source_wav" src="securimage_play.php?id=<?php echo uniqid() ?>" type="audio/wav">

      <!-- flash player fallback - delete if you don't want flash fallback -->
      <object type="application/x-shockwave-flash" data="securimage_play.swf?bgcol=%23ffffff&amp;icon_file=images%2Faudio_icon.png&amp;audio_file=securimage_play.php" height="32" width="32">
        <param name="movie" value="securimage_play.swf?bgcol=%23ffffff&amp;icon_file=images%2Faudio_icon.png&amp;audio_file=securimage_play.php" />
      </object>
    </audio>
  </div>
  <!-- div containing the HTML audio controls -->
  <div id="captcha_image2_audio_controls">
    <!-- play button and loading image that gets toggled when audio is loading -->
    <a tabindex="-1" class="captcha_play_button" href="securimage_play.php?id=<?php echo uniqid() ?>" onclick="return false">
      <img class="captcha_play_image" height="32" width="32" src="images/audio_icon.png" alt="Play CAPTCHA Audio" style="border: 0px">
      <img class="captcha_loading_image rotating" height="32" width="32" src="images/loading.png" alt="Loading audio" style="display: none">
    </a>
    <noscript>Enable Javascript for audio controls</noscript>
  </div>

  <!-- link to refresh the captcha image and audios -->
  <a tabindex="-1" style="border: 0" href="#" title="Refresh Image" onclick="document.getElementById('captcha_image2').src = 'securimage_show.php?' + Math.random(); captcha_image2_audioObj.refresh(); this.blur(); return false">
    <img height="32" width="32" src="images/refresh.png" alt="Refresh Image" onclick="this.blur()" style="border: 0px; vertical-align: bottom" />
  </a>
  <br>

  <!-- javascript code for refreshing and playing captcha audio -->
  <script type="text/javascript" src="securimage.js"></script>
  <script type="text/javascript">
    captcha_image2_audioObj = new SecurimageAudio({ audioElement: 'captcha_image2_audio', controlsElement: 'captcha_image2_audio_controls' });
    /*
    The SecurimageAudio object accepts a single object paramter with two properties:
      audioElement: the ID of the div containing the <audio> and <source> HTML tags
      controlsElement: the ID of the div containing the audio playback controls

    Note: securimage.js automatically finds the play and loading images by looking for images with
          class names of captcha_play_image and captcha_loading_image, respectively.

          The image inside of the controls div including the class name "captcha_play_image" will have
          click events registered to start/stop audio playback

          The image inside of the controls div including the class name "captcha_loading_image will be
          displayed with the audio is loading and hidden again once playback starts.

          Clicking the play button starts and stops audio playback.
    */
  </script>
  <div style="clear: both"></div>

  <!-- captcha input -->
  <label for="captcha_code2">Type the text:</label>
  <input type="text" name="captcha_code2" id="captcha_code2">
</div>

</body>
</html>
